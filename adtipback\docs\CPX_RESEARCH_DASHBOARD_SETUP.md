# CPX Research Dashboard Setup Guide

## 🎯 Quick Setup Instructions

### Step 1: Access CPX Research Publisher Dashboard
1. Go to: https://publisher.cpx-research.com
2. Login with your credentials
3. Navigate to your app settings (App ID: 28376)

### Step 2: Configure Main Postback URL

**Copy and paste this EXACT URL into the "Main Postback URL" field:**

```
https://api.adtip.in/api/cpx-research/postback?status={status}&trans_id={trans_id}&user_id={user_id}&subid_1={subid_1}&subid_2={subid_2}&amount_local={amount_local}&amount_usd={amount_usd}&offer_id={offer_ID}&hash={secure_hash}&ip_click={ip_click}&type={type}
```

### Step 3: Configure Security Settings

**Secure Hash**: `V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD`

### Step 4: Verify Settings

Make sure these settings are configured:

- ✅ **App ID**: 28376
- ✅ **Main Postback URL**: The complete URL from Step 2
- ✅ **Secure Hash**: V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD
- ✅ **IP Whitelist**: Should include your server IP if required

## 📋 Parameter Explanation

The postback URL includes these essential parameters:

| Parameter | Description | Example Value |
|-----------|-------------|---------------|
| `{status}` | 1 = completed, 2 = canceled/fraud | `1` |
| `{trans_id}` | Unique transaction ID | `cpx_12345678` |
| `{user_id}` | Your ADTIP user ID | `100` |
| `{subid_1}` | Custom parameter 1 (optional) | `mobile_app` |
| `{subid_2}` | Custom parameter 2 (optional) | `android` |
| `{amount_local}` | **REQUIRED** - Amount in INR | `10.50` |
| `{amount_usd}` | **REQUIRED** - Amount in USD | `0.13` |
| `{offer_ID}` | Survey/offer identifier | `survey_abc123` |
| `{secure_hash}` | MD5 validation hash | `a1b2c3d4...` |
| `{ip_click}` | User's IP address | `***********` |
| `{type}` | Transaction type | `complete` |

## ⚠️ Important Notes

### Required Parameters
- **`{amount_local}`** and **`{amount_usd}`** are MANDATORY
- Without these parameters, the system cannot calculate user rewards
- CPX Research will not send postbacks without these parameters configured

### Security
- The secure hash validates that postbacks are genuine
- Hash format: `MD5({trans_id}-V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD)`
- Only requests from CPX Research IPs are accepted in production

### Fraud Protection
- CPX Research will call the postback again with `status=2` for fraud/reversals
- This typically happens 15-60 days after the original completion
- The system automatically handles reversals by deducting the amount

## 🧪 Testing

### Test the Integration
1. Complete the dashboard setup
2. Use the CPX Research test feature in their dashboard
3. Check your server logs for incoming postbacks
4. Verify transactions appear in your database

### Development Testing
For development/testing, you can use:
```bash
# Test postback endpoint
curl "https://api.adtip.in/api/cpx-research/test-postback" \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"user_id": "100", "amount": "10.00"}'
```

## 🔍 Troubleshooting

### Common Issues

1. **Postbacks not received**
   - ✅ Verify the postback URL is exactly as shown above
   - ✅ Check that all parameters are included
   - ✅ Ensure your server is accessible from CPX Research IPs

2. **Invalid hash errors**
   - ✅ Verify secure hash matches: `V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD`
   - ✅ Check CPX Research dashboard configuration

3. **Amount calculation errors**
   - ✅ Ensure `{amount_local}` and `{amount_usd}` are in the URL
   - ✅ These parameters are essential for reward calculation

### Server Logs
Check your server logs for entries like:
```
[CPXResearchController] Received postback: {...}
[CPXResearchService] Processing postback: {...}
```

## ✅ Verification Checklist

Before going live, verify:

- [ ] Postback URL configured with ALL parameters
- [ ] Secure hash configured correctly
- [ ] Test postback successful
- [ ] Database tables created
- [ ] Server logs showing successful processing
- [ ] User wallet credited correctly
- [ ] Premium multiplier working (4x for premium users)

## 📞 Support

If you encounter issues:

1. **Check server logs** for detailed error messages
2. **Verify database** - ensure CPX Research tables exist
3. **Test locally** using the development endpoint
4. **Contact CPX Research support** for dashboard-related issues

---

**✨ Once configured correctly, the integration will automatically process survey completions and credit user wallets with appropriate rewards!**
