const crypto = require('crypto');

// Test data from your postback
const transId = '100774299814';
const receivedHash = '96f32df5559b3a6a9c1329ed880f6115';
const secret = 'V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD';

// Calculate expected hash
const expectedHash = crypto.createHash('md5').update(`${transId}-${secret}`).digest('hex');

console.log('Transaction ID:', transId);
console.log('Secret:', secret);
console.log('Hash string:', `${transId}-${secret}`);
console.log('Expected hash:', expectedHash);
console.log('Received hash:', receivedHash);
console.log('Match:', expectedHash === receivedHash);

// Let's also check if the secret might be different
console.log('\n--- Testing different possible secrets ---');

// Check if it might be without the secret
const hashWithoutSecret = crypto.createHash('md5').update(transId).digest('hex');
console.log('Hash without secret:', hashWithoutSecret);

// Check if it might be a different format
const hashDifferentFormat = crypto.createHash('md5').update(`${transId}${secret}`).digest('hex');
console.log('Hash without dash:', hashDifferentFormat);

// Check if the secret might be different (maybe from CPX dashboard)
const possibleSecrets = [
  'V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD',
  '28376', // App ID
  '', // Empty
  'test'
];

console.log('\n--- Testing possible secrets ---');
possibleSecrets.forEach(testSecret => {
  const testHash = crypto.createHash('md5').update(`${transId}-${testSecret}`).digest('hex');
  const match = testHash === receivedHash;
  console.log(`Secret: "${testSecret}" -> Hash: ${testHash} -> Match: ${match}`);
});
