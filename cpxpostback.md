Postback Settings 
Example: https://www.your-domain.com/file?status={status}&trans_id={trans_id}&user_id={user_id}&sub_id={subid}&sub_id_2={subid_2}&amount_local={amount_local}&amount_usd={amount_usd}&offer_id={offer_ID}&hash={secure_hash}&ip_click={ip_click}

Main Postback URL 
Important: In case a complete will be detected as fraud (usually within 15-60 days), we will call your postback URL again. If the &status={status} parameter is used, the status will change to =2 in the second call. We also recommend to use a unique transaction ID in your postback.
INFORMATION
Please enter a Main Postback URL Postback Url. This entry is mandatory. We call this URL every time a new event is created (e.g. User Complete a Survey, User screened out, User Rate a survey). When you enter a Screen Out Postback in EXPERT SETTINGS, this URL is no longer called for a Screen Out.

You can use the following placeholders:

{status} (1 = completed 2 = canceled) 
{trans_id} (unique ID )
{user_id} (your UserID)
{subid_1} (Your subId1)
{subid_2} (Your subId2)
{amount_local} amount in your currency 
{amount_usd} amount in USD
{ip_click} user click IP
{type} type return out, complete or bonus 
secure_hash} here we had a hash that you can validate the request the hash is a md5 hash: example: md5({trans_id}-yourappsecurehash) 

Postback Whitelist IP: ***********, 2a01:4f8:d0a:30ff::2 and ************

Postback Expert Settings 
Postback Url Screen Out
Postback Bonus/Rating
Postback Url Event Canceled
