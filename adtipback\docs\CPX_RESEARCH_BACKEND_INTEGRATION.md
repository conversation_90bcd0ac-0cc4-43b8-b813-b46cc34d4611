# CPX Research Backend Integration

## Overview

This document describes the backend implementation of CPX Research postback URL system for the ADTIP application. The integration handles survey completion notifications from CPX Research servers and processes user rewards accordingly.

## Architecture

### Components

1. **CPXResearchService** (`services/CPXResearchService.js`)
   - Core business logic for CPX Research integration
   - Handles postback validation and processing
   - Manages secure hash validation
   - Processes survey completions and reversals

2. **CPXResearchController** (`controllers/CPXResearchController.js`)
   - HTTP endpoint handlers
   - Request validation and response formatting
   - Integration with frontend reward system

3. **Database Tables** (`database/cpx_research_tables.sql`)
   - `cpx_research_transactions`: Stores postback transaction data
   - `survey_completions`: Tracks frontend survey completions
   - `cpx_research_config`: Configuration settings

## API Endpoints

### 1. Postback Endpoint
```
GET /api/cpx-research/postback
```

**Purpose**: Receives postback notifications from CPX Research servers when users complete surveys.

**Parameters** (Query String):
- `status`: 1 = completed, 2 = canceled/fraud
- `trans_id`: Unique transaction ID from CPX Research
- `user_id`: User ID from our system
- `amount_local`: Reward amount in local currency (INR)
- `amount_usd`: Reward amount in USD
- `offer_id`: Survey/offer ID
- `hash`: Security hash (MD5)
- `type`: Transaction type (complete, out, bonus)
- `subid_1`, `subid_2`: Custom parameters
- `ip_click`: User's IP address

**Security**:
- IP whitelist validation (production only)
- Secure hash validation using MD5(trans_id-app_secret)
- Duplicate transaction prevention

**Response**:
```json
{
  "success": true,
  "message": "Postback processed successfully",
  "transaction_id": "12345"
}
```

### 2. Credit Survey Reward
```
POST /api/credit-survey-reward
```

**Purpose**: Credits survey rewards from frontend (called by React Native app).

**Headers**: `Authorization: Bearer <token>`

**Body**:
```json
{
  "userId": 123,
  "amount": 10.50,
  "surveyId": "survey_123",
  "isPremium": true,
  "source": "cpx_research",
  "description": "CPX Research Survey Reward"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Survey reward credited successfully",
  "amount_credited": 42.00,
  "new_balance": 150.75
}
```

### 3. Transaction History
```
GET /api/cpx-research/transactions?page=1&limit=20
```

**Purpose**: Retrieves CPX Research transaction history for authenticated user.

**Headers**: `Authorization: Bearer <token>`

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "cpx_trans_id": "cpx_12345",
      "offer_id": "survey_abc",
      "amount_local": 10.50,
      "final_amount": 42.00,
      "status": "completed",
      "is_premium": true,
      "created_at": "2025-01-26T10:30:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalRecords": 100
  }
}
```

### 4. Test Postback (Development Only)
```
POST /api/cpx-research/test-postback
```

**Purpose**: Test postback processing in development environment.

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# CPX Research Configuration
CPX_RESEARCH_SECRET=V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD
NODE_ENV=production
```

### CPX Research Publisher Settings

1. **App ID**: 28376
2. **Postback URL**: `https://your-domain.com/api/cpx-research/postback`
3. **Secure Hash**: V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD

**Example Postback URL**:
```
https://your-domain.com/api/cpx-research/postback?status={status}&trans_id={trans_id}&user_id={user_id}&amount_local={amount_local}&amount_usd={amount_usd}&offer_id={offer_ID}&hash={secure_hash}&type={type}&ip_click={ip_click}
```

## Database Schema

### cpx_research_transactions
```sql
CREATE TABLE cpx_research_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  cpx_trans_id VARCHAR(255) UNIQUE NOT NULL,
  user_id INT NOT NULL,
  offer_id VARCHAR(255),
  amount_local DECIMAL(10,2) NOT NULL,
  amount_usd DECIMAL(10,2) NOT NULL,
  final_amount DECIMAL(10,2) NOT NULL,
  transaction_type VARCHAR(50) DEFAULT 'complete',
  status ENUM('pending','completed','reversed','failed'),
  is_premium BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## Security Features

### 1. IP Whitelist
Only requests from CPX Research servers are accepted:
- ***********
- 2a01:4f8:d0a:30ff::2
- ************

### 2. Secure Hash Validation
All postbacks must include a valid MD5 hash:
```javascript
const expectedHash = md5(`${trans_id}-${CPX_APP_SECRET}`);
```

### 3. Duplicate Prevention
Transaction IDs are unique to prevent duplicate processing.

## Premium User Handling

Premium users receive 4x reward multiplier:
- Regular user: 10 INR → 10 INR
- Premium user: 10 INR → 40 INR

Premium status is checked at the time of survey completion.

## Error Handling

### Postback Errors
- Invalid IP: 403 Forbidden
- Invalid hash: 200 OK with error message
- Missing parameters: 200 OK with error message
- Database errors: 200 OK with error message

**Note**: CPX Research expects 200 OK responses to prevent retries, even for errors.

### Frontend Errors
- Authentication required: 401 Unauthorized
- Invalid parameters: 400 Bad Request
- Server errors: 500 Internal Server Error

## Fraud Protection

### Reversals/Chargebacks
CPX Research may send postbacks with `status=2` for fraud detection:
1. Original transaction is marked as 'reversed'
2. Amount is deducted from user's wallet
3. Negative wallet entry is created

### Monitoring
- All transactions are logged
- Daily statistics views available
- Unusual patterns can be detected via database queries

## Installation

### 1. Run Database Migration
```bash
cd adtipback
node scripts/setup_cpx_research_tables.js
```

### 2. Configure Environment
Add CPX Research secret to `.env` file.

### 3. Update CPX Research Dashboard
Set postback URL in CPX Research publisher dashboard.

### 4. Test Integration
Use the test endpoint in development:
```bash
curl -X POST http://localhost:3000/api/cpx-research/test-postback \
  -H "Content-Type: application/json" \
  -d '{"user_id": "100", "amount": "10.00"}'
```

## Monitoring and Analytics

### Database Views

1. **cpx_research_summary**: User-level statistics
2. **cpx_research_daily_stats**: Daily performance metrics

### Key Metrics
- Total surveys completed
- Total rewards paid
- Reversal rate
- Average reward per user
- Daily active survey users

## Troubleshooting

### Common Issues

1. **Postback not received**
   - Check IP whitelist
   - Verify postback URL in CPX dashboard
   - Check server logs

2. **Invalid hash errors**
   - Verify CPX_RESEARCH_SECRET matches dashboard
   - Check hash calculation logic

3. **User not found**
   - Ensure user_id in postback matches database
   - Check user table foreign key constraints

4. **Duplicate transactions**
   - Normal behavior - system prevents duplicates
   - Check transaction status in database

### Logs
All CPX Research operations are logged with prefix `[CPXResearchService]` or `[CPXResearchController]`.

## Support

For issues with CPX Research integration:
1. Check server logs for detailed error messages
2. Verify database table structure
3. Test with development endpoint
4. Contact CPX Research support if needed
